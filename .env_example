# OpenAI API
OPENAI_API_KEY = <YOUR_OPENAI_API_KEY> --required when using OpenAI LLM

# Anthropic API
ANTHROPIC_API_KEY = <ANTHROPIC_API_KEY> --required when using Anthropic LLM

# Langchain API
LANGCHAIN_TRACING_V2 = <true|false> --required
LANGCHAIN_PROJECT = <project_name>
LANGCHAIN_API_KEY = <LANGCHAIN_API_KEY>

# LLM settings
LLM_PROVIDER = ANTHROPIC --required
LLM_MODEL = claude-3-haiku-20240307 --required
LLM_ANSWER_LANGUAGE = english --required

# Agent mongo database
MONGO_URI = <MONGO_DB_URI> --required
MONGO_DB_NAME = <DB_NAME> --required

# MintHCM mysql database
MINTDB_URI = <MINTDB_URI> --required
MINTDB_PORT = <MINTDB_PORT> --required
MINTDB_USER = <MINTDB_USER> --required
MINTDB_PASS = <MINTDB_PASSWORD> --required
MINTDB_DATABASE_NAME = <MINTDB_DATABASE_NAME> --required

# MintHCM API
MINT_API_URL = <MINT_API_URL> --required

# Agent API settings
API_IP = <API_IP> --required
API_PORT = <API_PORT> --required

# Logging configuration
LOG_LEVEL = <DEBUG|WARNING|ERROR> --required
LOG_TO_CONSOLE = <TRUE|FALSE> --required
LOG_FILE = e.g. /tmp/agent.log --required
LOG_COLORING = <TRUE|FALSE> --required
