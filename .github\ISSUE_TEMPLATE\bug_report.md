---
name: Bug report
about: Create a report to help us improve
title: 'Community Bug - '
labels: bug
assignees: AleksanderBak

---

<!-- Feature requests starting with # followed by a number (e.g., #123456) are linked to our internal task tracking system and represent features already being tracked or prioritized internally. If you'd like to submit a new feature request, please use the prefix "Community" for feature request and Community Bug for bug reports in the title (e.g., "Community - Add support for X") to indicate that it originates from our users. This helps us differentiate between internal and community-driven requests. Thank you for your contribution! -->

## ✅ Expected Behavior
<!--- Tell us what should happen -->

## 🛑 Current Behavior
<!--- Tell us what happens instead of the expected behavior -->

## 💡 Possible Solution
<!--- Not obligatory, but suggest a fix/reason for the bug, -->

## 🔄 Steps to Reproduce
<!--- Provide an unambiguous set of steps to reproduce this bug. -->
<!--- Include code to reproduce, if relevant -->
1.
2.
3.

## 🌍 Context (Environment)
<!--- How has this issue affected you? What are you trying to accomplish? -->
<!--- Providing context helps us come up with a solution that is most useful in the real world -->

## 📝 Detailed Description
<!--- Provide a detailed description of the change or addition you are proposing -->

## 🚀Possible Implementation
<!--- Not obligatory, but suggest an idea for implementing addition or change -->
