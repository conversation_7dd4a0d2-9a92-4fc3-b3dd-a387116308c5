[tool.poetry]
name = "mint_agent"
version = "0.1.0"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
aiohttp = "3.9.5"
aiosignal = "1.3.1"
annotated-types = "0.7.0"
anthropic = "0.34.1"
anyio = "4.4.0"
asttokens = "2.4.1"
asyncio = "3.4.3"
attrs = "23.2.0"
backcall = "0.2.0"
beautifulsoup4 = "4.12.3"
bleach = "6.1.0"
certifi = "2024.7.4"
charset-normalizer = "3.3.2"
click = "8.1.7"
colorama = "0.4.6"
dataclasses-json = "0.6.7"
decorator = "5.1.1"
defusedxml = "0.7.1"
distro = "1.9.0"
dnspython = "2.6.1"
docopt = "0.6.2"
email-validator = "2.2.0"
executing = "2.0.1"
fastapi = "0.112.2"
fastapi-cli = "0.0.5"
fastjsonschema = "2.20.0"
filelock = "3.15.4"
frozenlist = "1.4.1"
fsspec = "2024.6.1"
greenlet = "3.0.3"
h11 = "0.14.0"
httpcore = "1.0.5"
httptools = "0.6.1"
httpx = "0.27.0"
huggingface-hub = "0.23.5"
idna = "3.7"
inquirerpy = "0.3.4"
ipython = "8.12.3"
jedi = "0.19.1"
jinja2 = "3.1.4"
jiter = "0.5.0"
jsonpatch = "1.33"
jsonpointer = "3.0.0"
jsonschema = "4.23.0"
jsonschema-specifications = "2023.12.1"
jupyter-client = "8.6.2"
jupyter-core = "5.7.2"
jupyterlab-pygments = "0.3.0"
langchain = "0.2.14"
langchain-anthropic = "0.1.23"
langchain-community = "0.2.12"
langchain-core = "0.2.35"
langchain-openai = "0.1.22"
langchain-text-splitters = "0.2.2"
langgraph = "0.2.14"
langgraph-checkpoint = "1.0.6"
langsmith = "0.1.88"
loguru = "0.7.2"
markdown-it-py = "3.0.0"
markupsafe = "2.1.5"
marshmallow = "3.21.3"
matplotlib-inline = "0.1.7"
mdurl = "0.1.2"
mistune = "3.0.2"
motor = "3.5.1"
multidict = "6.0.5"
mypy-extensions = "1.0.0"
mysql-connector-python = "9.0.0"
nbclient = "0.10.0"
nbconvert = "7.16.4"
nbformat = "5.10.4"
numpy = "1.26.4"
oauthlib = "3.2.2"
openai = "1.41.0"
orjson = "3.10.6"
packaging = "24.1"
pandocfilters = "1.5.1"
parso = "0.8.4"
pfzy = "0.3.4"
pickleshare = "0.7.5"
pipreqs = "0.5.0"
platformdirs = "4.2.2"
prompt-toolkit = "3.0.47"
pure-eval = "0.2.2"
pydantic = "2.8.2"
pydantic-core = "2.20.1"
pygments = "2.18.0"
pymongo = "4.8.0"
python-dateutil = "2.9.0.post0"
python-dotenv = "1.0.1"
python-multipart = "0.0.9"
pywin32 = {version = "306", markers = "platform_system == \"Windows\""}
pyyaml = "6.0.1"
pyzmq = "26.1.0"
referencing = "0.35.1"
regex = "2024.7.24"
requests = "2.32.3"
requests-oauthlib = "2.0.0"
rich = "13.7.1"
rpds-py = "0.19.1"
shellingham = "1.5.4"
six = "1.16.0"
sniffio = "1.3.1"
soupsieve = "2.5"
sqlalchemy = "2.0.31"
stack-data = "0.6.3"
starlette = "0.37.2"
tenacity = "8.5.0"
tiktoken = "0.7.0"
tinycss2 = "1.3.0"
tokenizers = "0.19.1"
tornado = "6.4.1"
tqdm = "4.66.4"
traitlets = "5.14.3"
typer = "0.12.3"
typing-inspect = "0.9.0"
typing-extensions = "4.12.2"
urllib3 = "2.2.2"
uvicorn = "0.30.3"
watchfiles = "0.22.0"
wcwidth = "0.2.13"
webencodings = "0.5.1"
websockets = "13.0"
win32-setctime = {version = "1.1.0", markers = "platform_system == \"Windows\""}
yarg = "0.1.9"
yarl = "1.9.4"
cryptography = "^43.0.3"
inquirer = "^3.4.0"
termcolor = "^2.5.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
dev = "mint_agent.server:dev"
prod = "mint_agent.server:prod"
test_chat = "mint_agent.server:test_chat"
generate_credentials = "mint_agent.utils.generate_credentials:generate_credentials"
generate_key = "mint_agent.utils.generate_credentials:generate_encryption_key"
