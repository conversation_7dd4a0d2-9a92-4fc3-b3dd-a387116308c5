<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mint Agent Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .chat-container {
            width: 80vw;
            height: 80vh;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: #fff;
        }

        .chat-header {
            background-color: #60A5EE;
            color: white;
            padding: 10px;
            text-align: center;
        }

        .chat-messages {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
        }

        .chat-message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .user-message {
            text-align: left;
            font-weight: bolder;
            background-color: #8fccf7;
        }

        .ai-message {
            text-align: left;
            background-color: #e1ffc7;
        }

        .ai-debug {
            text-align: left;
            background-color: #f7f7f7;
        }

        .ai-tool {
            text-align: center;
            background-color: #c6abfb;
        }

        .error {
            background-color: #e72424;
            color: white;
            animation: fadeOut 5s forwards;
        }

        .chat-input {
            display: flex;
            padding: 10px;
            border-top: 1px solid #ddd;
        }

        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }

        .chat-input button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        button {
            margin: 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            color: white;
        }

        .confirm-button {
            background-color: #28a745;
        }

        .confirm-button:hover {
            background-color: #218838;
        }

        .decline-button {
            background-color: #dc3545;
        }

        .decline-button:hover {
            background-color: #c82333;
        }

        .chat-input button:hover {
            background-color: #0056b3;
        }

        .button-box {
            display: flex;
            justify-content: center;
        }   

        .connect {
            background-color: #28a745;
        }

        .connect:hover {
            background-color: #218838;
        }

        .close {
            background-color: #dc3545;
        }

        .close:hover {
            background-color: #c82333;
        }

        .reconnect {
            background-color: #ffc107;
        }

        .reconnect:hover {
            background-color: #e0a800;
        }

        .input-box {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            margin: 10px;
        }

        .input-box div {
            display: flex;
            align-items: center;
            margin: 5px;
        }

        .input-box label, .token_box label {
            margin-right: 5px;
            color: #07111D;
            font-weight: bold;
        }

        .input-box input, .token_box input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .status {
            background-color: #005fc4;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bolder;
            border-top: 1px solid #ddd;
        }

        .status.error {
            background-color: #dc3545;
        }

        .status.connected {
            background-color: #28a745;
        }

        .status.reconnecting {
            background-color: #ffc107;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        button:disabled:hover {
            background-color: #c9c8c8
        }

        .token_box {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 10px;
        }

        .token_box input {
            width: 60%;
        }
        
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="input-box">
                <div>
                    <label for="username">User ID:</label>
                    <input type="text" id="username" value="admin">
                </div>
                <div>
                    <label for="connection_id">Chat ID:</label>
                    <input type="text" id="connection_id" value="1">
                </div>
                <div>
                    <label for="advanced">Advanced user:</label>
                    <input type="checkbox" id="advanced" name="advanced" value="True">
                </div>
            </div>
            <div class="token_box">
                <label for="connection_id">Authentication token</label>
                <input type="text" id="token">
            </div>
            <div class="button-box">
                <button id="connect" class="connect">Connect</button>
                <button id="close" class="close">Close Connection</button>
                <button id="reconnect" class="reconnect">Reconnect</button>
            </div>
        </div>
        <div id="status" class="status">Disconnected</div>
        <div class="chat-messages" id="chatMessages">
        </div>
        <form class="chat-input" action="" onsubmit="sendMessage(event)">
            <input type="text" id="userInput" placeholder="Type a message..." value=""> 
            <button type="submit" id="submitButton">Send</button>
        </form>
        <div class="button-box">
            <button class="confirm-button" id="confirm">Confirm Tool</button>
            <button class="decline-button" id="decline">Decline Tool</button>
        </div>
    </div>
    <script>
        var ws;
        var lastAIMessageElement = null;

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById("confirm").disabled = true;
            document.getElementById("decline").disabled = true;
            document.getElementById("submitButton").disabled = true;
            document.getElementById("close").disabled = true;   
            document.getElementById("reconnect").disabled = true;
        });

        function connectWebSocket() {
            var chat_id = document.querySelector("#connection_id").value;
            var username = document.querySelector("#username").value;
            var advanced = document.querySelector("#advanced").checked;
            var token = document.querySelector("#token").value;

            ws = new WebSocket(`ws://localhost:5288/${username}/${chat_id}/${token}?advanced=${advanced}`);

            ws.onmessage = function(event) {
                var chatMessages = document.getElementById('chatMessages');
                var message = JSON.parse(event.data);
                console.log(message);
                if (message.type === "llm_text") {
                    var formattedContent = message.content.replace(/\n/g, '<br>');

                    if (lastAIMessageElement) {
                        lastAIMessageElement.innerHTML += formattedContent;
                    } else {
                        var messageElement = document.createElement('div');
                        messageElement.classList.add('chat-message', 'ai-message');
                        messageElement.innerHTML = message.content;
                        chatMessages.appendChild(messageElement);
                        lastAIMessageElement = messageElement;
                    }
                } else if (message.type === "accept_request") {
                    var messageElement = document.createElement('div');
                    messageElement.classList.add('chat-message', 'ai-tool');
                    messageElement.innerHTML = `
                        <strong>Tool Request:</strong><br>
                        <strong>Tool Name:</strong> ${message.tool_name}<br>
                        <strong>Input:</strong> ${message.tool_input}<br>
                    `;
                    chatMessages.appendChild(messageElement);
                    lastAIMessageElement = null; 

                    document.getElementById("submitButton").disabled = true;
                    document.getElementById("confirm").disabled = false;
                    document.getElementById("decline").disabled = false;
                } else if (message.type === "error") {
                    var messageElement = document.createElement('div');
                    messageElement.classList.add('chat-message', 'error');
                    messageElement.textContent = message.content;
                    chatMessages.appendChild(messageElement);
                } else if (message.type) {
                    if (message.type === "llm_end") {
                        lastAIMessageElement = null;
                    }
                    var messageElement = document.createElement('div');
                    messageElement.classList.add('chat-message', 'ai-debug');
                    messageElement.textContent = event.data;
                    chatMessages.appendChild(messageElement);
                    if (message.type == "llm_end") {
                        lastAIMessageElement = null;
                    }
                } else {
                    var messageElement = document.createElement('div');
                    messageElement.classList.add('chat-message', 'user-message');
                    messageElement.textContent = event.data;
                    chatMessages.appendChild(messageElement);
                }

                chatMessages.scrollTop = chatMessages.scrollHeight;
            };

            ws.onopen = function(event) {
                console.log("Connection established");
                updateStatus("Connected", "connected");
                document.getElementById("connect").disabled = true;
                document.getElementById("submitButton").disabled = false;
                document.getElementById("close").disabled = false;
                document.getElementById("reconnect").disabled = false;
            };

            ws.onclose = function(event) {
                console.log("Connection closed");
                updateStatus("Disconnected", "");
                document.getElementById("connect").disabled = false;
                document.getElementById("close").disabled = true;
                document.getElementById("reconnect").disabled = true;
                document.getElementById("submitButton").disabled = true;
            };

            ws.onerror = function(event) {
                console.log("Error: ", event);
                updateStatus("Error", "error");
            };
        }
        
        function updateStatus(status, className) {
            var statusElement = document.getElementById('status');
            statusElement.textContent = status;
            statusElement.className = `status ${className}`;
        }

        function addMessageToChat(content, className) {
            var chatMessages = document.getElementById('chatMessages');
            var messageElement = document.createElement('div');
            messageElement.classList.add('chat-message');
            messageElement.classList.add(className);
            messageElement.textContent = content;
            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function sendMessage(event) {
            var input = document.getElementById("userInput")
            var message = {
                type: "input",
                content: input.value
            }
            ws.send(JSON.stringify(message))
            addMessageToChat(input.value, 'user-message')
            input.value = ''
            event.preventDefault()
        }
        
        function closeWebSocket() {
            ws.close()
            updateStatus("Disconnected", "");
        }

        function reconnectWebSocket() {
            updateStatus("Reconnecting...", "reconnecting");
            closeWebSocket()
            connectWebSocket()
        }
        
        function sendConfirmation() {
            var message = {
                type: "tool_confirm",
            }
            addMessageToChat("Tool Confirmed", 'user-message')
            ws.send(JSON.stringify(message))

            document.getElementById("submitButton").disabled = false;
            document.getElementById("confirm").disabled = true;
            document.getElementById("decline").disabled = true;
        }

        function sendDecline() {
            var input = document.getElementById("userInput")
            var message = {
                type: "tool_reject",
                content: input.value
            }
            input.value = ''
            addMessageToChat("Tool Declined: " + message.content, 'user-message')
            ws.send(JSON.stringify(message))

            document.getElementById("submitButton").disabled = false;
            document.getElementById("confirm").disabled = true;
            document.getElementById("decline").disabled = true;
        }

        document.getElementById('connect').addEventListener('click', connectWebSocket);
        document.getElementById('close').addEventListener('click', closeWebSocket);
        document.getElementById('reconnect').addEventListener('click', reconnectWebSocket);
        document.getElementById("confirm").addEventListener("click", sendConfirmation)
        document.getElementById("decline").addEventListener("click", sendDecline)
    </script>
</body>
</html>