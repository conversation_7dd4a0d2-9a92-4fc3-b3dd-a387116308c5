---
name: Feature request
about: Suggest an idea for this project
title: 'Community - '
labels: ''
assignees: ''

---

<!-- Feature requests starting with # followed by a number (e.g., #123456) are linked to our internal task tracking system and represent features already being tracked or prioritized internally. If you'd like to submit a new feature request, please use the prefix "Community" for feature request and "Community Bug" for bug reports in the title (e.g., "Community - Add support for X") to indicate that it originates from our users. This helps us differentiate between internal and community-driven requests. Thank you for your contribution! -->

## 🔍 What's the problem?
<!-- A clear and concise description of what the problem is. Ex. I'm always frustrated when [...] -->

## 💡 Proposed Solution
<!-- A clear and concise description of what you want to happen. -->

## 🔄 Alternatives Considered
<!-- description of any alternative solutions or features you've considered. -->

## 📝 Additional context
<!-- Add any other context or screenshots about the feature request here -->
